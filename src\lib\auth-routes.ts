/**
 * Simple Route Configuration for Authentication
 * Minimal, production-grade solution with existing code structure
 */

// Routes that require authentication (redirect to login if not authenticated)
export const PROTECTED_ROUTES = [
  '/applications',
  '/opportunities/new',
  '/opportunities/posted'
]

// Routes only for unauthenticated users (redirect to dashboard if authenticated)
export const AUTH_ONLY_ROUTES = [
  '/login',
  '/signup',
  '/forgot-password'
]

/**
 * Check if a route requires authentication
 */
export function isProtectedRoute(pathname: string): boolean {
  return PROTECTED_ROUTES.some(route => pathname.startsWith(route))
}

/**
 * Check if a route is auth-only (for unauthenticated users)
 */
export function isAuthOnlyRoute(pathname: string): boolean {
  return AUTH_ONLY_ROUTES.includes(pathname)
}

/**
 * Get redirect path based on auth state and current route
 */
export function getAuthRedirect(pathname: string, isAuthenticated: boolean): string | null {
  if (isProtectedRoute(pathname) && !isAuthenticated) {
    return '/login'
  }
  
  if (isAuthOnlyRoute(pathname) && isAuthenticated) {
    return '/'
  }
  
  return null
}
