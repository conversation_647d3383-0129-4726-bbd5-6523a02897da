/**
 * Simple Route Configuration for Authentication
 * Minimal, production-grade solution with existing code structure
 */

// Routes that require authentication (redirect to login if not authenticated)
export const PROTECTED_ROUTES = [
  '/applications',
  '/opportunities/new',
  '/opportunities/posted'
]

// Routes only for unauthenticated users (redirect to dashboard if authenticated)
export const AUTH_ONLY_ROUTES = [
  '/login',
  '/signup',
  '/forgot-password'
]

/**
 * Check if a route requires authentication
 */
export function isProtectedRoute(pathname: string): boolean {
  return PROTECTED_ROUTES.some(route => {
    // Exact match for single routes
    if (route === pathname) return true

    // For routes ending with wildcard, check if pathname starts with the base
    if (route.endsWith('/*')) {
      const basePath = route.slice(0, -2)
      return pathname.startsWith(basePath + '/')
    }

    // For other routes, check if pathname starts with route followed by '/' or is exact match
    return pathname === route || pathname.startsWith(route + '/')
  })
}

/**
 * Check if a route is auth-only (for unauthenticated users)
 */
export function isAuthOnlyRoute(pathname: string): boolean {
  return AUTH_ONLY_ROUTES.includes(pathname)
}

/**
 * Get redirect path based on auth state and current route
 */
export function getAuthRedirect(pathname: string, isAuthenticated: boolean): string | null {
  // Home route is always public (accessible to everyone)
  if (pathname === '/') {
    return null
  }

  // Check if route requires authentication
  if (isProtectedRoute(pathname) && !isAuthenticated) {
    return '/login'
  }

  // Check if route is auth-only (for unauthenticated users)
  if (isAuthOnlyRoute(pathname) && isAuthenticated) {
    return '/'
  }

  return null
}
