/**
 * Auth Route Group Layout
 * 
 * Automatically protects all routes in the (auth) group.
 * Routes: /login, /signup, /forgot-password
 * 
 * This layout ensures auth-only routes redirect authenticated users.
 */

import { AuthGuard } from "@/components/auth/AuthGuard"

export default function AuthLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <AuthGuard>
      {children}
    </AuthGuard>
  )
}
