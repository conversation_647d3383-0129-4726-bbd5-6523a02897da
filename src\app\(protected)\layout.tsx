/**
 * Protected Route Group Layout
 * 
 * Automatically protects all routes in the (protected) group.
 * Routes: /applications, /opportunities/new, /opportunities/posted
 * 
 * This layout ensures protected routes redirect unauthenticated users to login.
 */

import { AuthGuard } from "@/components/auth/AuthGuard"

export default function ProtectedLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <AuthGuard>
      {children}
    </AuthGuard>
  )
}
