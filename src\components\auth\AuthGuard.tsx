/**
 * Modular Authentication Guard System
 * 
 * Production-grade route protection with zero page-level auth logic required.
 * Uses centralized route configuration for scalable auth management.
 */

"use client"

import { useEffect, type ReactNode } from "react"
import { useRouter, usePathname } from "next/navigation"
import { useAuth } from "@/contexts/auth/AuthContext"
import { getAuthRedirect } from "@/lib/route-config"
import { Loader2 } from "lucide-react"

interface AuthGuardProps {
  children: ReactNode
  fallback?: ReactNode
}

/**
 * Universal Auth Guard Component
 * 
 * Automatically handles all authentication logic based on route configuration.
 * No manual auth logic needed in pages - just wrap with this component.
 */
export function AuthGuard({ children, fallback }: AuthGuardProps) {
  const { isAuthenticated, isLoading } = useAuth()
  const router = useRouter()
  const pathname = usePathname()

  useEffect(() => {
    // Don't redirect while auth state is loading
    if (isLoading) return

    // Get redirect path based on route config and auth state
    const redirectPath = getAuthRedirect(pathname, isAuthenticated)
    
    if (redirectPath) {
      router.push(redirectPath)
    }
  }, [isAuthenticated, isLoading, pathname, router])

  // Show loading state while checking authentication
  if (isLoading) {
    return fallback || <AuthLoadingFallback />
  }

  // Check if redirect is needed
  const redirectPath = getAuthRedirect(pathname, isAuthenticated)
  if (redirectPath) {
    // Return null while redirecting to prevent flash of content
    return null
  }

  // Render children if auth requirements are met
  return <>{children}</>
}

/**
 * Default loading fallback component
 */
function AuthLoadingFallback() {
  return (
    <div className="flex h-screen w-screen items-center justify-center">
      <div className="text-center">
        <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
        <h2 className="text-xl font-semibold">Loading...</h2>
        <p className="text-muted-foreground">Please wait while we verify your session</p>
      </div>
    </div>
  )
}

/**
 * Convenience component for protected routes
 * Use this when you want to be explicit about requiring authentication
 */
export function ProtectedRoute({ children, fallback }: AuthGuardProps) {
  return <AuthGuard fallback={fallback}>{children}</AuthGuard>
}

/**
 * Convenience component for public routes
 * Use this when you want to be explicit about allowing public access
 */
export function PublicRoute({ children }: { children: ReactNode }) {
  return <>{children}</>
}

/**
 * Convenience component for auth-only routes (login, signup, etc.)
 * Use this when you want to be explicit about auth-only access
 */
export function AuthOnlyRoute({ children, fallback }: AuthGuardProps) {
  return <AuthGuard fallback={fallback}>{children}</AuthGuard>
}

/**
 * Higher-Order Component for wrapping pages with auth protection
 * 
 * Usage:
 * export default withAuthGuard(MyPage)
 */
export function withAuthGuard<P extends object>(
  Component: React.ComponentType<P>,
  fallback?: ReactNode
) {
  const WrappedComponent = (props: P) => (
    <AuthGuard fallback={fallback}>
      <Component {...props} />
    </AuthGuard>
  )
  
  WrappedComponent.displayName = `withAuthGuard(${Component.displayName || Component.name})`
  
  return WrappedComponent
}

/**
 * Hook for getting auth redirect information
 * Useful for conditional rendering based on auth requirements
 */
export function useAuthRedirect() {
  const { isAuthenticated, isLoading } = useAuth()
  const pathname = usePathname()
  
  const redirectPath = isLoading ? null : getAuthRedirect(pathname, isAuthenticated)
  
  return {
    isLoading,
    isAuthenticated,
    redirectPath,
    shouldRedirect: !!redirectPath
  }
}
