/**
 * Simple Auth Wrapper - Minimal Solution
 * Handles all auth redirects automatically with zero page-level logic
 */

"use client"

import { useEffect, type ReactNode } from "react"
import { useRouter, usePathname } from "next/navigation"
import { useAuth } from "@/contexts/auth/AuthContext"
import { getAuthRedirect } from "@/lib/auth-routes"
import { Loader2 } from "lucide-react"

interface AuthWrapperProps {
  children: ReactNode
}

export function AuthWrapper({ children }: AuthWrapperProps) {
  const { isAuthenticated, isLoading } = useAuth()
  const router = useRouter()
  const pathname = usePathname()

  useEffect(() => {
    if (isLoading) return

    const redirectPath = getAuthRedirect(pathname, isAuthenticated)
    if (redirectPath) {
      router.push(redirectPath)
    }
  }, [isAuthenticated, isLoading, pathname, router])

  // Show loading while checking auth
  if (isLoading) {
    return (
      <div className="flex h-screen w-screen items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
          <h2 className="text-xl font-semibold">Loading...</h2>
          <p className="text-muted-foreground">Please wait while we verify your session</p>
        </div>
      </div>
    )
  }

  // Always render content - redirects happen in useEffect
  // This ensures pages show their content while redirect is processing
  return <>{children}</>
}
