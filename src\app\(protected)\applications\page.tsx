"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Eye, MessageCircle, Calendar, Music } from "lucide-react"
import Link from "next/link"
import type { Application } from "../../../features/opportunites/types"

// Mock applications data
const mockApplications: Application[] = [
  {
    id: "1",
    opportunityId: "1",
    opportunity: {
      id: "1",
      title: "Session Guitarist for Indie Rock EP",
      role: "Guitarist",
      description: "",
      postedBy: {
        id: "2",
        name: "The Midnight Band",
        type: "band",
        avatar: "/placeholder.svg?height=40&width=40",
      },
      genre: ["Indie", "Rock"],
      timeline: { startDate: "2024-07-01", endDate: "2024-07-31" },
      location: { type: "remote" },
      payment: { type: "paid", amount: 5000, currency: "₹" },
      requirements: [],
      status: "open",
      applicationsCount: 8,
      createdAt: "2024-01-10",
      updatedAt: "2024-01-10",
    },
    applicantId: "current-user",
    applicant: {
      id: "current-user",
      name: "You",
    },
    status: "pending",
    message: "I have 5 years of experience playing indie rock and would love to contribute...",
    appliedAt: "2024-01-11",
  },
  {
    id: "2",
    opportunityId: "2",
    opportunity: {
      id: "2",
      title: "Jazz Drummer for Album Recording",
      role: "Drummer",
      description: "",
      postedBy: {
        id: "3",
        name: "Reena Saini",
        type: "artist",
        avatar: "/placeholder.svg?height=40&width=40",
      },
      genre: ["Jazz"],
      timeline: { startDate: "2024-06-15", endDate: "2024-07-30" },
      location: { type: "remote" },
      payment: { type: "paid", amount: 8000, currency: "₹" },
      requirements: [],
      status: "open",
      applicationsCount: 12,
      createdAt: "2024-01-12",
      updatedAt: "2024-01-12",
    },
    applicantId: "current-user",
    applicant: {
      id: "current-user",
      name: "You",
    },
    status: "accepted",
    message: "I have 8 years of jazz drumming experience and would love to contribute...",
    appliedAt: "2024-01-13",
  },
  {
    id: "3",
    opportunityId: "3",
    opportunity: {
      id: "3",
      title: "Vocalist for Electronic Project",
      role: "Vocalist",
      description: "",
      postedBy: {
        id: "4",
        name: "Alex Producer",
        type: "person",
        avatar: "/placeholder.svg?height=40&width=40",
      },
      genre: ["Electronic"],
      timeline: { startDate: "2024-08-01", endDate: "2024-09-15" },
      location: { type: "hybrid" },
      payment: { type: "negotiable" },
      requirements: [],
      status: "open",
      applicationsCount: 15,
      createdAt: "2024-01-14",
      updatedAt: "2024-01-14",
    },
    applicantId: "current-user",
    applicant: {
      id: "current-user",
      name: "You",
    },
    status: "rejected",
    message: "I'm a versatile vocalist with experience in electronic music...",
    appliedAt: "2024-01-16",
  },
]

const statusColors = {
  pending: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300",
  accepted: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300",
  rejected: "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300",
}

export default function ApplicationsPage() {
  const [statusFilter, setStatusFilter] = useState<string>("all")

  const filteredApplications = mockApplications.filter(app => 
    statusFilter === "all" || app.status === statusFilter
  )

  return (
    <div className="min-h-screen bg-background">
      <div className="px-4 md:px-8 lg:px-16 py-8">
        {/* Header */}
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-8">
          <div>
            <h1 className="text-3xl font-bold">My Applications</h1>
            <p className="text-muted-foreground">Track your opportunity applications</p>
          </div>
          <Link href="/opportunities">
            <Button>Browse Opportunities</Button>
          </Link>
        </div>

        {/* Filters */}
        <div className="flex flex-col sm:flex-row gap-4 mb-6">
          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger className="w-full sm:w-48">
              <SelectValue placeholder="Filter by status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Applications</SelectItem>
              <SelectItem value="pending">Pending</SelectItem>
              <SelectItem value="accepted">Accepted</SelectItem>
              <SelectItem value="rejected">Rejected</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Applications List */}
        <div className="space-y-6">
          {filteredApplications.length === 0 ? (
            <Card>
              <CardContent className="flex flex-col items-center justify-center py-12">
                <Music className="h-12 w-12 text-muted-foreground mb-4" />
                <h3 className="text-lg font-semibold mb-2">No applications found</h3>
                <p className="text-muted-foreground text-center mb-4">
                  {statusFilter === "all" 
                    ? "You haven't applied to any opportunities yet." 
                    : `No ${statusFilter} applications found.`}
                </p>
                <Link href="/opportunities">
                  <Button>Browse Opportunities</Button>
                </Link>
              </CardContent>
            </Card>
          ) : (
            filteredApplications.map((application) => (
              <Card key={application.id} className="overflow-hidden">
                <CardContent className="p-6">
                  <div className="flex flex-col lg:flex-row gap-6">
                    {/* Main Content */}
                    <div className="flex-1">
                      <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-4 mb-4">
                        <div>
                          <h3 className="text-xl font-semibold mb-2">
                            {application.opportunity.title}
                          </h3>
                          <div className="flex items-center gap-2 text-sm text-muted-foreground mb-2">
                            <Avatar className="h-6 w-6">
                              <AvatarImage src={application.opportunity.postedBy.avatar} />
                              <AvatarFallback>
                                {application.opportunity.postedBy.name.charAt(0)}
                              </AvatarFallback>
                            </Avatar>
                            <span>{application.opportunity.postedBy.name}</span>
                          </div>
                          <div className="flex flex-wrap gap-2 mb-3">
                            {application.opportunity.genre.map((g) => (
                              <Badge key={g} variant="secondary" className="text-xs">
                                {g}
                              </Badge>
                            ))}
                          </div>
                        </div>
                        <div className="flex flex-col sm:items-end gap-2">
                          <Badge className={statusColors[application.status]}>
                            {application.status.charAt(0).toUpperCase() + application.status.slice(1)}
                          </Badge>
                          <div className="flex items-center gap-1 text-sm text-muted-foreground">
                            <Calendar className="h-4 w-4" />
                            <span>Applied {new Date(application.appliedAt).toLocaleDateString()}</span>
                          </div>
                        </div>
                      </div>

                      {/* Application Message */}
                      <div className="bg-muted/50 rounded-lg p-4 mb-4">
                        <div className="flex items-center gap-2 mb-2">
                          <MessageCircle className="h-4 w-4 text-muted-foreground" />
                          <span className="text-sm font-medium">Your Application</span>
                        </div>
                        <p className="text-sm text-muted-foreground">
                          {application.message}
                        </p>
                      </div>

                      {/* Opportunity Details */}
                      <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 text-sm">
                        <div>
                          <span className="font-medium">Timeline:</span>
                          <p className="text-muted-foreground">
                            {new Date(application.opportunity.timeline.startDate).toLocaleDateString()} - {new Date(application.opportunity.timeline.endDate).toLocaleDateString()}
                          </p>
                        </div>
                        <div>
                          <span className="font-medium">Location:</span>
                          <p className="text-muted-foreground capitalize">
                            {application.opportunity.location.type}
                          </p>
                        </div>
                        <div>
                          <span className="font-medium">Payment:</span>
                          <p className="text-muted-foreground">
                            {application.opportunity.payment.type === "paid" 
                              ? `${application.opportunity.payment.currency}${application.opportunity.payment.amount?.toLocaleString()}`
                              : application.opportunity.payment.type}
                          </p>
                        </div>
                      </div>
                    </div>

                    {/* Actions */}
                    <div className="flex lg:flex-col gap-2">
                      <Link href={`/opportunities/${application.opportunity.id}`} className="flex-1 lg:flex-none">
                        <Button variant="outline" size="sm" className="w-full">
                          <Eye className="h-4 w-4 mr-2" />
                          View Details
                        </Button>
                      </Link>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))
          )}
        </div>
      </div>
    </div>
  )
}
