/**
 * Centralized Route Configuration for Authentication
 * 
 * This file defines which routes require authentication and their behavior.
 * Industry standard approach for scalable auth management.
 */

export type RouteType = 'public' | 'protected' | 'auth-only'

export interface RouteConfig {
  /** Route pattern (supports wildcards) */
  pattern: string
  /** Route type determines auth behavior */
  type: RouteType
  /** Where to redirect if auth requirement not met */
  redirectTo?: string
  /** Optional description for documentation */
  description?: string
}

/**
 * Route Configuration Rules:
 * 
 * - 'public': Accessible to everyone (no auth required)
 * - 'protected': Requires authentication (redirects to login if not authenticated)
 * - 'auth-only': Only for unauthenticated users (redirects to dashboard if authenticated)
 */
export const ROUTE_CONFIG: RouteConfig[] = [
  // Authentication routes - only for unauthenticated users
  {
    pattern: '/login',
    type: 'auth-only',
    redirectTo: '/',
    description: 'Login page - redirects authenticated users to dashboard'
  },
  {
    pattern: '/signup',
    type: 'auth-only',
    redirectTo: '/',
    description: 'Signup page - redirects authenticated users to dashboard'
  },
  {
    pattern: '/forgot-password',
    type: 'auth-only',
    redirectTo: '/',
    description: 'Password reset - redirects authenticated users to dashboard'
  },
  {
    pattern: '/auth/callback',
    type: 'public',
    description: 'OAuth callback - accessible to all'
  },

  // Protected routes - require authentication
  {
    pattern: '/applications',
    type: 'protected',
    redirectTo: '/login',
    description: 'User applications - requires authentication'
  },
  {
    pattern: '/opportunities/new',
    type: 'protected',
    redirectTo: '/login',
    description: 'Create opportunity - requires authentication'
  },
  {
    pattern: '/opportunities/posted',
    type: 'protected',
    redirectTo: '/login',
    description: 'Posted opportunities - requires authentication'
  },

  // Public routes - accessible to everyone
  {
    pattern: '/',
    type: 'public',
    description: 'Home page - accessible to all'
  },
  {
    pattern: '/opportunities',
    type: 'public',
    description: 'Browse opportunities - accessible to all'
  },
  {
    pattern: '/opportunities/*',
    type: 'public',
    description: 'Opportunity details - accessible to all'
  },
  {
    pattern: '/artist',
    type: 'public',
    description: 'Artist pages - accessible to all'
  },
  {
    pattern: '/album',
    type: 'public',
    description: 'Album pages - accessible to all'
  },
  {
    pattern: '/song',
    type: 'public',
    description: 'Song pages - accessible to all'
  },
  {
    pattern: '/playlist',
    type: 'public',
    description: 'Playlist pages - accessible to all'
  }
]

/**
 * Get route configuration for a given path
 */
export function getRouteConfig(pathname: string): RouteConfig | null {
  // Find exact match first
  const exactMatch = ROUTE_CONFIG.find(route => route.pattern === pathname)
  if (exactMatch) return exactMatch

  // Find wildcard match
  const wildcardMatch = ROUTE_CONFIG.find(route => {
    if (route.pattern.endsWith('/*')) {
      const basePath = route.pattern.slice(0, -2)
      return pathname.startsWith(basePath)
    }
    return false
  })
  
  return wildcardMatch || null
}

/**
 * Determine if a route requires authentication
 */
export function isProtectedRoute(pathname: string): boolean {
  const config = getRouteConfig(pathname)
  return config?.type === 'protected'
}

/**
 * Determine if a route is auth-only (for unauthenticated users)
 */
export function isAuthOnlyRoute(pathname: string): boolean {
  const config = getRouteConfig(pathname)
  return config?.type === 'auth-only'
}

/**
 * Get redirect path for auth state mismatch
 */
export function getAuthRedirect(pathname: string, isAuthenticated: boolean): string | null {
  const config = getRouteConfig(pathname)
  
  if (!config) {
    // Default behavior for unconfigured routes: require auth
    return isAuthenticated ? null : '/login'
  }

  switch (config.type) {
    case 'protected':
      return isAuthenticated ? null : (config.redirectTo || '/login')
    
    case 'auth-only':
      return isAuthenticated ? (config.redirectTo || '/') : null
    
    case 'public':
    default:
      return null
  }
}
