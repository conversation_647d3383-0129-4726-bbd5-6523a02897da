'use client';

import { But<PERSON> } from "@/components/ui/button";
import ThemeToggle from "@/components/shared/theme/ThemeToggle";
import LanguageToggle from "@/components/shared/language/LanguageToggle";
import { HomeIcon, LogIn, LogOut, User } from "lucide-react";
import { useAuth as useAuthContext } from "@/contexts/auth/AuthContext";
import { useRouter } from "next/navigation";

export default function Header() {
  const { isAuthenticated, isLoading, user, signOut } = useAuthContext();
  const router = useRouter();

  // Navigation helpers
  const goToLogin = () => router.push('/login');
  const goToSignup = () => router.push('/signup');
  const goToDashboard = () => router.push('/');

  const handleSignOut = async () => {
    await signOut();
  };

  return (
    <header className="flex justify-between items-center p-6 bg-background border-b">
      <div className="flex items-center gap-2 cursor-pointer"  onClick={()=>goToDashboard()}>
        <HomeIcon/>
        <h2 className="text-2xl font-bold">Smash Music</h2>
      </div>
      <div className="flex items-center gap-3">
        {!isLoading && (
          <>
            {isAuthenticated ? (
              <div className="flex items-center gap-3">
                <span className="text-sm text-muted-foreground">
                  Welcome, {user?.email || user?.username}
                </span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleSignOut}
                  className="gap-2"
                >
                  <LogOut className="w-4 h-4" />
                  Sign Out
                </Button>
              </div>
            ) : (
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={goToLogin}
                  className="gap-2"
                >
                  <LogIn className="w-4 h-4" />
                  Sign In
                </Button>
                <Button
                  size="sm"
                  onClick={goToSignup}
                  className="gap-2"
                >
                  <User className="w-4 h-4" />
                  Sign Up
                </Button>
              </div>
            )}
          </>
        )}
        <LanguageToggle />
        <ThemeToggle />
      </div>
    </header>
  );
}
